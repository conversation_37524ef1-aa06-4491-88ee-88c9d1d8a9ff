<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SupplementTypeConstant } from '@haierbusiness-front/common-libs'; // 🔧 新增：导入补充条目枚举

// 类型定义
interface RelatedBillItem {
  id: string;
  sequenceNumber: number;
  date: string;
  project: string;
  category: string;
  contractPrice: number;
  contractQuantity: number;
  billPrice: number;
  billQuantity: number;
  relatedBill: string;
}

interface RelatedBillDialogProps {
  visible: boolean;
  billType: 'invoice' | 'waterBill';
  billData: any;
  demandInfo?: any; // 需求信息，包含 stays 数据
  existingRelatedBills?: RelatedBillItem[]; // 已有的关联账单数据
  excludedBillIds?: string[]; // 需要排除的账单ID（已被其他发票/水单关联）
  initialAttachmentAmount?: number; // 🔧 新增：初始附件金额
}

interface RelatedBillDialogEmits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: any): void;
  (e: 'updateStaysInvoiceId', data: {
    invoiceTempId: string;
    billType: 'invoice' | 'waterBill';
    selectedItemsByType: {
      stays: string[];
      places: string[];
      caterings: string[];
      vehicles: string[];
      attendants: string[];
      activities: string[];
      presents: string[]; // 🔧 新增：礼品
      others: string[]; // 🔧 新增：其他
      serviceFees: string[]; // 🔧 新增：全单服务费
    };
  }): void;
  (e: 'updateRelatedAmount', data: {
    invoiceTempId: string;
    billType: 'invoice' | 'waterBill';
    totalAmount: number;
    relatedBills: RelatedBillItem[];
  }): void;
  (e: 'updateAttachmentAmount', data: {
    invoiceTempId: string;
    billType: 'invoice' | 'waterBill';
    attachmentAmount: number;
  }): void; // 🔧 新增：附件金额更新事件
}

const props = defineProps<RelatedBillDialogProps>();
const emit = defineEmits<RelatedBillDialogEmits>();

// 响应式数据
const isAddMode = ref(false); // 是否为添加模式
const selectedRowKeys = ref<string[]>([]);
const loading = ref(false);
const editableAttachmentAmount = ref<number>(0); // 可编辑的附件总金额
const addedBillIds = ref<string[]>([]); // 记录已添加到关联账单的原始数据ID

// 分页相关
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 筛选条件
const filterForm = ref({
  hotelPlan: '',
  project: '',
  planDate: null as any, // 日期选择器返回的是 dayjs 对象
});

// 项目选项 - 基础选项
const baseProjectOptions = [
  { value: '住宿', label: '住宿' },
  { value: '会场', label: '会场' },
  { value: '用餐', label: '用餐' },
  { value: '用车', label: '用车' },
  { value: '服务人员', label: '服务人员' },
  { value: '拓展活动', label: '拓展活动' }, // 🔧 修改：活动改为拓展活动
  { value: '礼品', label: '礼品' }, // 🔧 新增
  { value: '其他', label: '其他' }, // 🔧 新增
  { value: '全单服务费', label: '全单服务费' }, // 🔧 新增
];

// 🔧 新增：动态项目选项（包含补充条目中的具体项目）
const projectOptions = computed(() => {
  const options = [...baseProjectOptions];

  // 🔧 添加补充条目中的具体项目名称（使用枚举）
  if (props.demandInfo?.additionalItems) {
    const additionalProjects = new Set<string>();
    props.demandInfo.additionalItems.forEach((item: any) => {
      // 🔧 使用枚举获取项目类型的文本显示
      const typeEnum = SupplementTypeConstant.ofType(item.type);
      const projectName = typeEnum ? typeEnum.desc : (item.itemName || '补充项目');

      if (projectName && projectName.trim()) {
        additionalProjects.add(projectName.trim());
      }
    });

    // 将补充条目的项目添加到选项中
    additionalProjects.forEach(projectName => {
      options.push({ value: projectName, label: projectName });
    });
  }

  return options;
});

// 已关联账单数据（初始为空，从后端加载）
const relatedBillList = ref<RelatedBillItem[]>([]);

// 可选择的账单数据（从 demandInfo.stays 生成）
const availableBillList = ref<RelatedBillItem[]>([]);

// 计算属性
const attachmentTotalAmount = computed(() => {
  return relatedBillList.value.reduce((sum, item) => sum + item.contractPrice * item.contractQuantity, 0);
});

const billTotalAmount = computed(() => {
  return relatedBillList.value.reduce((sum, item) => sum + item.billPrice * item.billQuantity, 0);
});

const modalTitle = computed(() => {
  if (isAddMode.value) {
    return '添加关联账单';
  }
  return `关联账单详情 - ${props.billType === 'invoice' ? '发票' : '水单'}`;
});

// 表格列定义
const viewColumns = [
  { title: '序号', dataIndex: 'sequenceNumber', width: 60 },
  { title: '日期', dataIndex: 'date', width: 100 },
  { title: '项目', dataIndex: 'project', width: 80 },
  { title: '类别', dataIndex: 'category', width: 80 },
  { title: '竞价单价金额(人民币)', dataIndex: 'contractPrice', width: 150 },
  { title: '竞价数量', dataIndex: 'contractQuantity', width: 80 },
  { title: '账单单价金额(人民币)', dataIndex: 'billPrice', width: 150 },
  { title: '账单实际数量', dataIndex: 'billQuantity', width: 120 },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }, // 🔧 新增：操作列
];

const addColumns = [
  { title: '序号', dataIndex: 'sequenceNumber', width: 60 },
  { title: '日期', dataIndex: 'date', width: 100 },
  { title: '项目', dataIndex: 'project', width: 80 },
  { title: '类别', dataIndex: 'category', width: 80 },
  { title: '竞价单价金额(人民币)', dataIndex: 'contractPrice', width: 120 },
  { title: '竞价数量', dataIndex: 'contractQuantity', width: 80 },
  { title: '账单单价金额(人民币)', dataIndex: 'billPrice', width: 120 },
  { title: '账单实际数量', dataIndex: 'billQuantity', width: 120 },
];

// 筛选后的数据
const filteredAvailableBillList = computed(() => {
  let list = availableBillList.value;
  console.log(availableBillList.value,"最开始的list");
  
  console.log(props.excludedBillIds,"props.excludedBillIds");
  // 过滤掉已被其他发票/水单关联的账单
  if (props.excludedBillIds && props.excludedBillIds.length > 0) {
    list = list.filter((item,index) => {
      return !props.excludedBillIds?.includes(item.id);
    });
  }
  
  if (filterForm.value.project) {    
    list = list.filter((item) => item.project === filterForm.value.project);
  }
  
  if (filterForm.value.hotelPlan) {
    list = list.filter(
      (item) => item.project.includes(filterForm.value.hotelPlan) || item.category.includes(filterForm.value.hotelPlan),
    );
  }

  if (filterForm.value.planDate) {
    // 格式化选中的日期为 YYYY-MM-DD 格式
    const selectedDate = filterForm.value.planDate.format('YYYY-MM-DD');
    list = list.filter((item) => {
      // 使用统一的日期格式化函数
      const formattedItemDate = formatDate(item.date);
      return formattedItemDate === selectedDate;
    });
  }

  // 更新分页总数
  pagination.value.total = list.length;
  console.log(list,"最后list");
  return list;
});

// 计算是否全选（基于当前页面）
const isAllSelected = computed(() => {
  const currentPageData = getCurrentPageData();
  return currentPageData.length > 0 && currentPageData.every((item) => selectedRowKeys.value.includes(item.id));
});

// 格式化日期为 YYYY-MM-DD 格式
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  // 如果已经是 YYYY-MM-DD 格式，直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }
  // 如果包含时间部分，只取日期部分
  if (dateStr.includes(' ')) {
    return dateStr.split(' ')[0];
  }
  // 其他格式尝试转换
  try {
    const date = new Date(dateStr);
    return date.toISOString().split('T')[0];
  } catch {
    return dateStr;
  }
};

// 获取当前页面数据
const getCurrentPageData = () => {
  const start = (pagination.value.current - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredAvailableBillList.value.slice(start, end);
};

// 从 demandInfo 的各个字段生成可选择的账单数据
const generateAvailableBillList = () => {
  const billList: RelatedBillItem[] = [];
  let sequenceNumber = 1;

  // 处理住宿数据
  if (props.demandInfo?.stays) {
    props.demandInfo.stays.forEach((stay: any) => {
      const stayId = stay.id || stay.tempId || stay.miceDemandStayId || stay.miceSchemeStayId || sequenceNumber;

      billList.push({
        id: `stay_${stayId}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(stay.demandDate || ''),
        project: '住宿',
        category: '原需求',
        contractPrice: stay.schemeUnitPrice || 0,
        contractQuantity: stay.schemeRoomNum || 0,
        billPrice: stay.schemeUnitPrice || 0,
        billQuantity: stay.schemeRoomNum || 0,
        relatedBill: '',
      });
    });
  }

  // 处理会场数据
  if (props.demandInfo?.places) {
    props.demandInfo.places.forEach((place: any) => {
      // 会场总价 = 会场单价 + LED价格 + 茶歇价格
      let totalPrice = place.schemeUnitPlacePrice || 0;
      if (place.hasLed && place.schemeUnitLedPrice && place.schemeLedNum) {
        totalPrice += place.schemeUnitLedPrice * place.schemeLedNum;
      }
      if (place.hasTea && place.teaEachTotalPrice && place.schemePersonNum) {
        totalPrice += place.teaEachTotalPrice * place.schemePersonNum;
      }

      billList.push({
        id: `place_${place.id || place.tempId || place.miceDemandPlaceId || place.miceSchemePlaceId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(place.demandDate || ''),
        project: '会场',
        category: '原需求',
        contractPrice: totalPrice,
        contractQuantity: 1, // 会场通常按天计算
        billPrice: totalPrice,
        billQuantity: 1,
        relatedBill: '',
      });
    });
  }

  // 处理用餐数据
  if (props.demandInfo?.caterings) {
    props.demandInfo.caterings.forEach((catering: any) => {
      billList.push({
        id: `catering_${catering.id || catering.tempId || catering.miceDemandCateringId || catering.miceSchemeCateringId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(catering.demandDate || ''),
        project: '用餐',
        category: '原需求',
        contractPrice: catering.schemeUnitPrice || 0,
        contractQuantity: catering.schemePersonNum || 0,
        billPrice: catering.schemeUnitPrice || 0,
        billQuantity: catering.schemePersonNum || 0,
        relatedBill: '',
      });
    });
  }

  // 处理用车数据
  if (props.demandInfo?.vehicles) {
    props.demandInfo.vehicles.forEach((vehicle: any) => {
      billList.push({
        id: `vehicle_${vehicle.id || vehicle.tempId || vehicle.miceDemandVehicleId || vehicle.miceSchemeVehicleId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(vehicle.demandDate || ''),
        project: '用车',
        category: '原需求',
        contractPrice: vehicle.schemeUnitPrice || 0,
        contractQuantity: vehicle.schemeVehicleNum || 1,
        billPrice: vehicle.schemeUnitPrice || 0,
        billQuantity: vehicle.schemeVehicleNum || 1,
        relatedBill: '',
      });
    });
  }

  // 处理服务人员数据
  if (props.demandInfo?.attendants) {
    props.demandInfo.attendants.forEach((attendant: any) => {
      billList.push({
        id: `attendant_${attendant.id || attendant.tempId || attendant.miceDemandAttendantId || attendant.miceSchemeAttendantId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(attendant.demandDate || ''),
        project: '服务人员',
        category: '原需求',
        contractPrice: attendant.schemeUnitPrice || 0,
        contractQuantity: attendant.schemePersonNum || 1,
        billPrice: attendant.schemeUnitPrice || 0,
        billQuantity: attendant.schemePersonNum || 1,
        relatedBill: '',
      });
    });
  }

  // 处理活动数据
  if (props.demandInfo?.activities) {
    props.demandInfo.activities.forEach((activity: any) => {
      billList.push({
        id: `activity_${activity.id || activity.tempId || activity.miceDemandActivityId || activity.miceSchemeActivityId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(activity.demandDate || ''),
        project: '拓展活动', // 🔧 修改：活动改为拓展活动
        category: '原需求',
        contractPrice: activity.schemeUnitPrice || 0,
        contractQuantity: activity.schemePersonNum || 1,
        billPrice: activity.schemeUnitPrice || 0,
        billQuantity: activity.schemePersonNum || 1,
        relatedBill: '',
      });
    });
  }

  // 🔧 新增：处理礼品数据
  if (props.demandInfo?.presents) {
    props.demandInfo.presents.forEach((present: any) => {
      // 礼品可能有多个明细，每个明细作为一个账单项
      if (present.presentDetails && present.presentDetails.length > 0) {
        present.presentDetails.forEach((detail: any) => {
          billList.push({
            id: `present_${present.id || present.tempId || sequenceNumber}_${detail.id || detail.tempId || Date.now()}`,
            sequenceNumber: sequenceNumber++,
            date: formatDate(present.demandDate || ''),
            project: '礼品',
            category: detail.presentName || '礼品明细',
            contractPrice: detail.schemeUnitPrice || 0,
            contractQuantity: detail.schemeNum || 1,
            billPrice: detail.schemeUnitPrice || 0,
            billQuantity: detail.schemeNum || 1,
            relatedBill: '',
          });
        });
      } else {
        // 如果没有明细，使用礼品主记录
        billList.push({
          id: `present_${present.id || present.tempId || sequenceNumber}`,
          sequenceNumber: sequenceNumber++,
          date: formatDate(present.demandDate || ''),
          project: '礼品',
          category: '礼品方案',
          contractPrice: present.schemeTotalPrice || 0,
          contractQuantity: 1,
          billPrice: present.schemeTotalPrice || 0,
          billQuantity: 1,
          relatedBill: '',
        });
      }
    });
  }

  // 🔧 新增：处理其他方案数据
  if (props.demandInfo?.others) {
    props.demandInfo.others.forEach((other: any) => {
      billList.push({
        id: `other_${other.id || other.tempId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(other.demandDate || ''),
        project: '其他',
        category: other.otherName || '其他方案',
        contractPrice: other.schemeUnitPrice || 0,
        contractQuantity: other.schemeNum || 1,
        billPrice: other.schemeUnitPrice || 0,
        billQuantity: other.schemeNum || 1,
        relatedBill: '',
      });
    });
  }

  // 🔧 新增：处理补充条目数据
  if (props.demandInfo?.additionalItems) {

    props.demandInfo.additionalItems.forEach((item: any) => {
      // 🔧 修复：使用枚举获取项目类型的文本显示
      const typeEnum = SupplementTypeConstant.ofType(item.type);
      const projectName = typeEnum ? typeEnum.desc : (item.itemName || '补充项目');

      billList.push({
        id: `additionalItem_${item.id || item.tempId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(item.occurDate || item.demandDate || ''), // 🔧 修复：使用 occurDate
        project: projectName, // 🔧 使用枚举的文本显示（如"客损"、"其他"等）
        category: '补充条目',
        contractPrice: item.billUnitPrice || 0, // 🔧 修复：使用 billUnitPrice
        contractQuantity: item.billNum || 1,    // 🔧 修复：使用 billNum
        billPrice: item.billUnitPrice || 0,     // 🔧 修复：使用 billUnitPrice
        billQuantity: item.billNum || 1,        // 🔧 修复：使用 billNum
        relatedBill: '',
      });
    });
  }

  // 🔧 新增：处理全单服务费数据
  if (props.demandInfo?.serviceFee) {
    const serviceFee = props.demandInfo.serviceFee;
    billList.push({
      id: `serviceFee_${serviceFee.id || serviceFee.tempId || 'main'}`,
      sequenceNumber: sequenceNumber++,
      date: '', // 全单服务费通常没有具体日期
      project: '全单服务费',
      category: '服务费',
      contractPrice: serviceFee.schemeServiceFeeReal || 0,
      contractQuantity: 1,
      billPrice: serviceFee.schemeServiceFeeReal || 0,
      billQuantity: 1,
      relatedBill: '',
    });
  } else {
  }

  availableBillList.value = billList;
};

// 方法
const handleCancel = () => {
  emit('update:visible', false);
  isAddMode.value = false;
  selectedRowKeys.value = [];
  resetFilter();
  // 🔧 修复：重置分页到第一页
  resetPagination();
};

const handleAddRelatedBill = () => {
  isAddMode.value = true;
  // 自动选中已添加过的数据
  selectedRowKeys.value = [...addedBillIds.value];
};

const handleBackToView = () => {
  isAddMode.value = false;
  selectedRowKeys.value = [];
  resetFilter();
  // 🔧 修复：重置分页到第一页
  resetPagination();
};

const handleConfirmAdd = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  const selectedItems = filteredAvailableBillList.value.filter((item) => selectedRowKeys.value.includes(item.id));

  // 只添加新选中的数据（排除已存在的）
  const newSelectedItems = selectedItems.filter((item) => !addedBillIds.value.includes(item.id));

  // 添加到关联账单列表
  const newItems = newSelectedItems.map((item) => ({
    ...item,
    id: `new_${Date.now()}_${item.id}`,
    sequenceNumber: relatedBillList.value.length + newSelectedItems.indexOf(item) + 1,
    relatedBill: '查看>>',
  }));

  relatedBillList.value.push(...newItems);

  // 记录所有选中的原始数据ID（包括新添加和已存在的）
  addedBillIds.value = [...new Set([...addedBillIds.value, ...selectedRowKeys.value])];
  console.log(props.billData?.tempId,"props.billData?.tempId");
  
  // 根据发票或水单类型，将tempId关联到选中的记录
  if (props.billData?.tempId) {
    // 按项目类型分组选中的ID
    const selectedItemsByType = {
      stays: selectedRowKeys.value.filter(id => id.startsWith('stay_')),
      places: selectedRowKeys.value.filter(id => id.startsWith('place_')),
      caterings: selectedRowKeys.value.filter(id => id.startsWith('catering_')),
      vehicles: selectedRowKeys.value.filter(id => id.startsWith('vehicle_')),
      attendants: selectedRowKeys.value.filter(id => id.startsWith('attendant_')),
      activities: selectedRowKeys.value.filter(id => id.startsWith('activity_')),
      presents: selectedRowKeys.value.filter(id => id.startsWith('present_')), // 🔧 新增：礼品
      others: selectedRowKeys.value.filter(id => id.startsWith('other_')), // 🔧 新增：其他
      additionalItems: selectedRowKeys.value.filter(id => id.startsWith('additionalItem_')), // 🔧 新增：其他
      serviceFees: selectedRowKeys.value.filter(id => id.startsWith('serviceFee_')), // 🔧 新增：全单服务费
    };

    // 发送关联更新事件，包含所有类型的数据
    emit('updateStaysInvoiceId', {
      invoiceTempId: props.billData.tempId,
      billType: props.billType, // 'invoice' 或 'waterBill'
      selectedItemsByType: selectedItemsByType
    });

    // 计算总金额并发送更新关联金额事件，同时传递关联账单数据
    const totalAmount = relatedBillList.value.reduce((sum, item) => sum + item.contractPrice * item.contractQuantity, 0);
    console.log(relatedBillList.value,'传递完整的关联账单数据');
    
    emit('updateRelatedAmount', {
      invoiceTempId: props.billData.tempId,
      billType: props.billType,
      totalAmount: totalAmount,
      relatedBills: [...relatedBillList.value] // 传递完整的关联账单数据
    });
    
  }

  // 返回查看模式
  handleBackToView();
};

const resetFilter = () => {
  filterForm.value = {
    hotelPlan: '',
    project: '',
    planDate: null,
  };
};

// 🔧 新增：删除关联账单
const handleDeleteRelatedBill = (record: RelatedBillItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除这条关联账单吗？\n项目：${record.project}\n类别：${record.category}`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      // 从关联账单列表中移除
      const index = relatedBillList.value.findIndex(item => item.id === record.id);
      if (index > -1) {
        relatedBillList.value.splice(index, 1);

        // 重新计算序号
        relatedBillList.value.forEach((item, idx) => {
          item.sequenceNumber = idx + 1;
        });

        // 从已添加ID列表中移除（提取原始ID）
        const originalId = extractOriginalBillId(record.id);
        const addedIndex = addedBillIds.value.indexOf(originalId);
        if (addedIndex > -1) {
          addedBillIds.value.splice(addedIndex, 1);
        }

        // 🔧 修复：删除操作需要精确清除被删除项目的关联ID
        // 但不能影响其他项目的关联ID
        if (props.billData?.tempId) {
          // 构造要清除关联的数据结构（只包含被删除的项目）
          const selectedItemsByType = {
            stays: originalId.startsWith('stay_') ? [originalId] : [],
            places: originalId.startsWith('place_') ? [originalId] : [],
            caterings: originalId.startsWith('catering_') ? [originalId] : [],
            vehicles: originalId.startsWith('vehicle_') ? [originalId] : [],
            attendants: originalId.startsWith('attendant_') ? [originalId] : [],
            activities: originalId.startsWith('activity_') ? [originalId] : [],
            presents: originalId.startsWith('present_') ? [originalId] : [],
            others: originalId.startsWith('other_') ? [originalId] : [],
            additionalItems: originalId.startsWith('additionalItem_') ? [originalId] : [], // 🔧 新增：补充条目
            serviceFees: originalId.startsWith('serviceFee_') ? [originalId] : [],
          };

          // 🔧 新增：发送特殊的删除事件，使用特殊标识来区分删除操作
          emit('updateStaysInvoiceId', {
            invoiceTempId: `DELETE_${props.billData.tempId}`, // 使用特殊前缀标识删除操作
            billType: props.billType,
            selectedItemsByType: selectedItemsByType
          });
        }

        // 更新关联金额和账单数据
        if (props.billData?.tempId) {
          const totalAmount = relatedBillList.value.reduce((sum, item) => sum + item.contractPrice * item.contractQuantity, 0);

          emit('updateRelatedAmount', {
            invoiceTempId: props.billData.tempId,
            billType: props.billType,
            totalAmount: totalAmount,
            relatedBills: [...relatedBillList.value]
          });
        }
      }
    }
  });
};

// 🔧 新增：提取原始账单ID的辅助函数
const extractOriginalBillId = (billId: string): string => {
  // 如果是新添加的格式 'new_timestamp_originalId'，提取原始ID
  if (billId.startsWith('new_')) {
    const parts = billId.split('_');
    if (parts.length >= 3) {
      return parts.slice(2).join('_'); // 去掉 'new_' 和时间戳部分
    }
  }
  return billId;
};

// 🔧 新增：重置分页的方法
const resetPagination = () => {
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
};

const toggleSelectAll = () => {
  const currentPageData = getCurrentPageData();
  const currentPageIds = currentPageData.map((item) => item.id);

  if (isAllSelected.value) {
    // 取消当前页所有选择
    selectedRowKeys.value = selectedRowKeys.value.filter((id) => !currentPageIds.includes(id));
  } else {
    // 选中当前页所有项（保留其他页的选择）
    const newSelectedKeys = [...new Set([...selectedRowKeys.value, ...currentPageIds])];
    selectedRowKeys.value = newSelectedKeys;
  }
};

const handleSearch = () => {
  // 查询逻辑，这里可以添加搜索过滤功能

  // 重置分页到第一页
  pagination.value.current = 1;
  // 保持选择状态，不清空
  message.success(`查询完成，找到 ${filteredAvailableBillList.value.length} 条记录`);
};

const handleSelectChange = (selectedKeys: string[]) => {
  selectedRowKeys.value = selectedKeys;
};

// 分页变化处理
const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  // 不清空选择状态，保持跨页选择
};

// 格式化金额显示
const formatAmount = (amount: number) => {
  return `${amount}元`;
};

// 监听attachmentTotalAmount变化，同步到可编辑金额（仅在没有初始金额时）
watch(
  attachmentTotalAmount,
  (newValue) => {
    // 🔧 修改：只有在没有传入初始金额时才自动同步计算值
    if (props.billData?.initialAttachmentAmount === undefined) {
      editableAttachmentAmount.value = newValue;
    }
  },
  { immediate: true },
);

// 🔧 新增：监听用户修改附件金额
const handleAttachmentAmountChange = () => {
  // 当用户修改金额时，通知父组件更新
  if (props.billData?.tempId && typeof editableAttachmentAmount.value === 'number') {
    console.log('🔧 用户修改附件金额:', editableAttachmentAmount.value);
    emit('updateAttachmentAmount', {
      invoiceTempId: props.billData.tempId,
      billType: props.billType,
      attachmentAmount: editableAttachmentAmount.value
    });
  }
};

// 初始化已有的关联账单数据
const initializeExistingRelatedBills = () => {
  if (props.existingRelatedBills && props.existingRelatedBills.length > 0) {
    relatedBillList.value = [...props.existingRelatedBills];

    // 记录已添加的账单ID，防止重复添加
    addedBillIds.value = props.existingRelatedBills.map(bill => {
      // 从bill.id中提取原始ID
      if (bill.id.startsWith('new_')) {
        // 如果是新添加的格式，提取原始ID
        const parts = bill.id.split('_');
        if (parts.length >= 3) {
          return parts.slice(2).join('_'); // 去掉 'new_' 和时间戳部分
        }
      }
      return bill.id;
    });
  } else {
    relatedBillList.value = [];
    addedBillIds.value = [];
  }
};

// 监听 demandInfo 变化
watch(
  () => props.demandInfo,
  (newValue) => {
    if (newValue) {
      // 生成可选择的账单数据
      generateAvailableBillList();
    }
  },
  { immediate: true, deep: true },
);

// 监听 existingRelatedBills 变化，但不要在用户操作过程中重置数据
watch(
  () => props.existingRelatedBills,
  (newValue, oldValue) => {
    // 只有在弹窗首次打开或者外部数据真正变化时才初始化
    if (!isAddMode.value && relatedBillList.value.length === 0) {
      initializeExistingRelatedBills();
    }
  },
  { immediate: true, deep: true },
);

// 监听弹窗显示状态，每次打开时重新初始化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 🔧 新增：优先使用传入的初始金额，否则使用计算值
      if (props.billData?.initialAttachmentAmount !== undefined) {
        editableAttachmentAmount.value = props.billData.initialAttachmentAmount;
        console.log('🔧 使用传入的初始附件金额:', props.billData.initialAttachmentAmount);
      } else {
        editableAttachmentAmount.value = attachmentTotalAmount.value;
        console.log('🔧 使用计算的附件金额:', attachmentTotalAmount.value);
      }

      initializeExistingRelatedBills();
      // 🔧 修复：每次打开弹框都重置到第一页
      resetPagination();
      // 🔧 修复：重置筛选条件
      resetFilter();
    }
  },
  { immediate: true },
);

watch(
  () => props.excludedBillIds,
  (newExcludedIds) => {
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <a-modal
    :open="visible"
    :title="modalTitle"
    width="1400px"
    :footer="null"
    :body-style="{  overflow: 'auto', padding: '20px' }"
    @cancel="handleCancel"
  >
    <!-- 查看模式 -->
    <div v-if="!isAddMode" class="view-mode">
      <!-- 金额统计 -->
      <div class="amount-summary">
        <span class="amount-item">
          附件总金额:
          <input
            v-model.number="editableAttachmentAmount"
            class="amount-input"
            type="number"
            :min="0"
            :step="0.01"
            placeholder="请输入金额"
            @blur="handleAttachmentAmountChange"
            @change="handleAttachmentAmountChange"
          />元
          <span class="amount-tip">（来自发票总金额，可修改）</span>
        </span>
        <span class="amount-item">
          账单合计金额: <span class="amount-value red">{{ formatAmount(billTotalAmount) }}</span>
        </span>
      </div>

      <!-- 关联账单表格 -->
      <div class="table-wrapper">
        <a-table
          :columns="viewColumns"
          :data-source="relatedBillList"
          :pagination="false"
          size="small"
          :scroll="{ x: 1000, y: 300 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractPrice'">
              {{ formatAmount(record.contractPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'billPrice'">
              {{ formatAmount(record.billPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'relatedBill'">
              <a-button type="link" size="small">
                {{ record.relatedBill }}
              </a-button>
            </template>
            <!-- 🔧 新增：操作列 - 删除按钮 -->
            <template v-else-if="column.dataIndex === 'action'">
              <a-button
                type="link"
                size="small"
                danger
                @click="() => handleDeleteRelatedBill(record)"
              >
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 添加按钮 -->
      <div class="add-button-wrapper">
        <a-button type="link" @click="handleAddRelatedBill"> + 增加账单关联 </a-button>
      </div>
    </div>

    <!-- 添加模式 -->
    <div v-else class="add-mode">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <a-row :align="'middle'">
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>方案酒店：</label>
          </a-col>
          <a-col :span="4">
            <a-input v-model:value="filterForm.hotelPlan" placeholder="请输入" allow-clear />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>类型：</label>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="filterForm.project"
              :options="projectOptions"
              placeholder="请选择"
              allow-clear
              style="width: 100%"
            />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>方案日期：</label>
          </a-col>
          <a-col :span="4">
            <a-date-picker
              v-model:value="filterForm.planDate"
              placeholder="请选择"
              allow-clear
              style="width: 100%"
            />
          </a-col>
          <a-col :span="6" style="text-align: left; padding-left: 10px">
            <a-button style="margin-right: 10px" @click="resetFilter">重置</a-button>
            <a-button type="primary" @click="handleSearch">查询</a-button>
          </a-col>
        </a-row>
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="18" style="text-align: left">
            <a-button @click="toggleSelectAll">{{ isAllSelected ? '取消全选' : '全选' }}</a-button>
            <!-- <span v-if="props.excludedBillIds && props.excludedBillIds.length > 0" style="margin-left: 16px; color: #666; font-size: 12px;">
              已过滤 {{ props.excludedBillIds.length }} 条已关联的账单
            </span> -->
          </a-col>
          <!-- <a-col :span="6" style="text-align: right; color: #666; font-size: 12px;">
            共 {{ filteredAvailableBillList.length }} 条可选账单
          </a-col> -->
        </a-row>
      </div>

      <!-- 可选择账单表格 -->
      <div class="table-wrapper">
        <a-table
          :columns="addColumns"
          :data-source="filteredAvailableBillList"
          :pagination="pagination"
          size="small"
          :scroll="{ x: 1000, y: 500 }"
          row-key="id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: handleSelectChange,
            type: 'checkbox',
          }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractPrice'">
              {{ formatAmount(record.contractPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'billPrice'">
              {{ formatAmount(record.billPrice) }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button @click="handleBackToView"> 返回 </a-button>
        <a-button type="primary" @click="handleConfirmAdd"> 确认添加 </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.view-mode {
  .amount-summary {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 4px;
    display: flex;
    gap: 32px;

    .amount-item {
      font-size: 14px;
      color: #333;

      .amount-value {
        font-weight: 600;
        margin-left: 4px;

        &.red {
          color: #ff4d4f;
        }
      }

      .amount-input {
        border: none;
        border-bottom: 1px solid #d9d9d9;
        background: transparent;
        outline: none;
        font-weight: 600;
        margin: 0 4px;
        padding: 2px 4px;
        min-width: 80px;
        text-align: center;

        &:focus {
          border-bottom: 2px solid #1890ff;
        }

        &::placeholder {
          color: #bfbfbf;
          font-weight: normal;
        }
      }

      .amount-tip {
        font-size: 12px;
        color: #999;
        margin-left: 8px;
        font-weight: normal;
      }
    }
  }

  .add-button-wrapper {
    margin-top: 16px;
    text-align: center;
    padding: 12px;
    border-top: 1px solid #d9d9d9;
    background-color: #fafafa;
  }
}

.add-mode {
  .filter-section {
    margin-bottom: 16px;
    border-radius: 4px;
  }

  .action-buttons {
    margin-top: 16px;
    text-align: center;
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}

.table-wrapper {
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 500;
      text-align: center;
    }

    .ant-table-tbody > tr > td {
      text-align: center;
    }
  }
}
</style>
