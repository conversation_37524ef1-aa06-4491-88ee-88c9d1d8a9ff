<script setup lang="ts">
// 方案变更-其他方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, inject, defineProps, defineEmits } from 'vue';
import { OthersArr } from '@haierbusiness-front/common-libs';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  bargainSchemeInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  isSchemeBargain: {
    // 是否议价
    type: Boolean,
    default: false,
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  processNode: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['otherPriceEmit', 'schemeOtherEmit']);

const schemeChangeAddFunc = inject('schemeChangeAddFunc', () => {});
const schemeChangeDelFunc = inject('schemeChangeDelFunc', () => {});

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const schemePlanLabelList = ['项目', '数量', '单位', '总预算', '规则说明'];

const isVerifyFailed = ref<boolean>(false); // 校验是否失败
// 其他需求
const otherParams = ref<OthersArr>({
  // 需求其他需求明细
  // demandDate: null, // 需求日期
  num: null, // 数量
  itemName: null, // 项目
  unit: null, // 单位
  specs: '以服务商提报为准', // 规格描述
  schemeTotalPrice: null, // 总预算
});

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice = newSchemeList.value[index].biddingPrice * newSchemeList.value[index].num;
  }

  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('otherPriceEmit', subtotal.value);
  }
};

const addScheme = (idx: number) => {
  isVerifyFailed.value = false;

  newSchemeList.value.push({
    isSchemeChangeAdd: true, // 是否变更方案新增

    ...otherParams.value,
  });

  schemeChangeAddFunc();

  // priceCalcFun();
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  schemeChangeDelFunc();
  // priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const otherTempSave = () => {
  emit('schemeOtherEmit', [...newSchemeList.value]);
};

// 校验
const otherSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    // 只校验新增
    if (e.isSchemeChangeAdd) {
      isVerifyFailed.value = true;

      if (isVerPassed === false) return;

      if (!e.itemName) {
        message.error('请填写其他方案' + (i + 1) + '项目');

        isVerPassed = false;
        anchorJump('changeSchemeOtherId');
        return;
      }

      if (e.num === null || e.num === undefined) {
        message.error('请填写其他方案' + (i + 1) + '数量');

        isVerPassed = false;
        anchorJump('changeSchemeOtherId');
        return;
      }

      if (!e.unit) {
        message.error('请填写其他方案' + (i + 1) + '单位');

        isVerPassed = false;
        anchorJump('changeSchemeOtherId');
        return;
      }

      if (e.schemeTotalPrice === null || e.schemeTotalPrice === undefined) {
        message.error('请填写其他方案' + (i + 1) + '总预算');

        isVerPassed = false;
        anchorJump('changeSchemeOtherId');
        return;
      }

      if (!e.specs) {
        message.error('请填写其他方案' + (i + 1) + '规格描述');

        isVerPassed = false;
        anchorJump('changeSchemeOtherId');
        return;
      }
    }
  });

  if (isVerPassed) {
    otherTempSave();
  }

  return isVerPassed;
};

defineExpose({ otherSub, otherTempSave });

onMounted(async () => {
  // console.log('%c [ 其他方案 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeInfo.others);
  if ((props.schemeInfo && props.schemeInfo.others) || (props.schemeCacheInfo && props.schemeCacheInfo.others)) {
    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeInfo))?.others || [];

    if (props.isSchemeCache && props.schemeCacheInfo) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheInfo?.others || [];
    } else if (props.isSchemeBargain && props.bargainSchemeInfo) {
      // 议价、议价查看
      newSchemeList.value = props.bargainSchemeInfo?.others || [];

      newSchemeList.value.forEach((e, index) => {
        e.schemeTotalPrice = e.schemeTotalPrice || oldSchemeList.value[index].schemeTotalPrice;
      });
    } else {
      // 变更查看
      newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));
    }

    // 小计
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (e.schemeTotalPrice) {
        subtotal.value += e.schemeTotalPrice;
      }
    });

    emit('otherPriceEmit', subtotal.value);
  }
});
</script>

<template>
  <!-- 其他方案 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>其他方案</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '其他' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.itemName || '-' }}
                </template>
                {{ item.itemName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.num || '-' }}
                </template>
                {{ item.num || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                </template>
                {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeTotalPrice ? '¥' + formatNumberThousands(item.schemeTotalPrice) : '-' }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeTotalPrice">
                {{ item.schemeTotalPrice + '(元/总预算)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r" id="changeSchemeOtherId">
        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '其他' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.itemName ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.itemName || '-' }}
                    </template>
                    {{ item.itemName || '-' }}
                  </a-tooltip>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="item.itemName"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写项目"
                    :bordered="false"
                    :maxlength="200"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.num === null || item.num === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeBargainingEdit', 'schemeChangeView', 'schemeWinBidView'].includes(
                      schemeChangeType,
                    )
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.num || '-' }}
                    </template>
                    {{ item.num || '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 30px)"
                    v-model:value="item.num"
                    @blur="changePrice(idx)"
                    placeholder="请填写数量"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <!-- <span>元</span> -->
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.unit ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.unit || '-' }}
                    </template>
                    {{ item.unit || '-' }}
                  </a-tooltip>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="item.unit"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写单位"
                    :bordered="false"
                    :maxlength="200"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeTotalPrice === null || item.schemeTotalPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                    </template>
                    {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeTotalPrice"
                    @blur="changePrice(idx)"
                    placeholder="请填写总预算"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="99999.99"
                    :precision="2"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.specs ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.specs || '-' }}
                    </template>
                    {{ item.specs || '-' }}
                  </a-tooltip>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="item.specs"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写规格描述"
                    :bordered="false"
                    :maxlength="500"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeTotalPrice ? '¥' + formatNumberThousands(item.schemeTotalPrice) : '-' }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeTotalPrice">
                {{ item.schemeTotalPrice + '(元/总预算)' }}
              </div>
            </div>

            <!-- 操作 -->
            <div
              class="action_icons"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                schemeChangeType === 'schemeChangeEdit' &&
                item.isSchemeChangeAdd
              "
            >
              <a-popconfirm
                :title="'确认删除其他方案' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div
          v-if="
            ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
            schemeChangeType === 'schemeChangeEdit'
          "
          class="add_scheme_plan mt20"
          @click="addScheme(index)"
        >
          <div class="plan_add_img mr8"></div>
          <span>新增其他方案</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_material.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
