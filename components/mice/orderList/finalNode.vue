<script setup lang="ts">
import arrow from '@/assets/image/finalNode/arrow.png'; //箭头
import Bidding from '@/assets/image/finalNode/Bidding.png'; //竞价
import demand from '@/assets/image/finalNode/demand.png'; //需求
import execute from '@/assets/image/finalNode/execute.png'; //执行
import plan from '@/assets/image/finalNode/plan.png'; //方案
import settlement from '@/assets/image/finalNode/settlement.png'; //结算
import { ref, onMounted, reactive, onUnmounted, defineProps, computed, onBeforeMount, nextTick, Ref } from 'vue';
import { Tag as hTag } from 'ant-design-vue';
// import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
// import { CardItem } from '@haierbusiness-front/common-libs';
// import { useRoute, useRouter } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';
// import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';

// const route = useRoute();
// const router = useRouter();

const props = defineProps({
  nodeId: {
    type: String,
  },
});

console.log(props.nodeId, 'props.nodeId');

const nodeDetails = resolveParam(props.nodeId);

const nodeDetailsList: any = ref([]);

const getList = async () => {
  console.log(nodeDetails.miceId, '节点id');

  nodeDetailsList.value = await miceBidManOrderListApi.nodeDetails({ id: Number(nodeDetails.miceId) });
};
onMounted(() => {
  getList();
});
let pictureType = null;
let stateColor = '';
let fontColor = '';
let nowColor = '';
let borderColor = '';
let backgroundColor = '';
let stageColor = '';
let boxShadow = '';
let outline = '';

const pictureJudgment = (item, index) => {
  // 阶段图片映射
  const stagePicture = {
    需求: demand,
    方案: plan,
    竞价: Bidding,
    执行: execute,
    结算: settlement,
  };

  // 当前执行颜色映射
  const activeColors = {
    需求: '#1868DB',
    方案: '#FAAD14',
    竞价: '#58b7e9',
    执行: '#FF6A00',
    结算: '#52C41A',
  };

  pictureType = stagePicture[item.stageName];
  stageColor = activeColors[item.stageName];
};

const colorJudgment = (item, node, index: number, num: number) => {
  // 当前执行颜色映射
  const activeColors = {
    需求: '#1868DB',
    方案: '#FAAD14',
    竞价: '#58b7e9',
    执行: '#FF6A00',
    结算: '#52C41A',
  };
  // 背景颜色映射
  const activeBgColors = {
    需求: 'rgba(24, 104, 219, 0.1)',
    方案: 'rgba(250, 173, 20, 0.1)',
    竞价: 'rgba(88, 183, 233,0.1)',
    执行: 'rgba(255, 106, 0, 0.1)',
    结算: 'rgba(82, 196, 26, 0.1)',
  };
  fontColor = activeColors[item.stageName];
  backgroundColor = activeBgColors[item.stageName];

  if (node.nowExecute) {
    fontColor = '#ffffff';
    backgroundColor = activeColors[item.stageName];
    outline = `4px solid ${activeBgColors[item.stageName]}`;
    borderColor = `1px solid ${activeColors[item.stageName]}`;
    boxShadow = `0px 6px 8px 0px ${activeBgColors[item.stageName]}`;
  }

  // 找到当前执行的节点位置
  let currentIndex = -1;
  let currentStage = -1;

  if (nodeDetailsList.value) {
    nodeDetailsList.value.forEach((item, index) => {
      item.nodes.forEach((res, column) => {
        if (res.nowExecute == true) {
          currentIndex = Number(column);
          currentStage = Number(index);
        }
      });
    });
  }

  // 判断当前节点与执行节点的位置关系
  const isBeforeExecuteNode = index > currentStage || (index === currentStage && num > currentIndex);

  if (isBeforeExecuteNode) {
    // 如果在当前执行节点之后，显示灰色
    fontColor = '#86909C';
    stateColor = '#86909C';
    backgroundColor = '#F2F3F5';
    borderColor = '1px solid #E5E6EB';
    boxShadow = 'none';
    outline = 'none';
  }
};

interface DragState {
  isDragging: boolean;
  startX: number;
  scrollLeft: number;
}

const containerRefs = ref<Record<number, HTMLElement>>({});
const dragState = ref<DragState>({
  isDragging: false,
  startX: 0,
  scrollLeft: 0,
});

const setContainerRef = (el: HTMLElement | null, index: number) => {
  if (el) containerRefs.value[index] = el;
};

const startDrag = (e: MouseEvent, index: number) => {
  e.preventDefault();
  const container = containerRefs.value[index];
  if (!container) return;

  dragState.value = {
    isDragging: true,
    startX: e.clientX - container.getBoundingClientRect().left,
    scrollLeft: container.scrollLeft,
  };
  container.style.cursor = 'grabbing';
};

const onDrag = (e: MouseEvent) => {
  if (!dragState.value.isDragging) return;
  e.preventDefault();

  const container = Object.values(containerRefs.value).find((el) => el.style.cursor === 'grabbing');
  if (!container) return;

  const x = e.clientX - container.getBoundingClientRect().left;
  const walk = (x - dragState.value.startX) * 2;
  container.scrollLeft = dragState.value.scrollLeft - walk;
};

const stopDrag = () => {
  dragState.value.isDragging = false;
  Object.values(containerRefs.value).forEach((el) => {
    el.style.cursor = '';
  });
};
</script>
<template>
  <div class="process-nodes">
    <!-- 流程节点区域 -->
    <div class="flow-content">
      <div
        class="flow-row"
        v-for="(item, index) in nodeDetailsList"
        :key="item.stageName"
        :ref="pictureJudgment(item, index)"
      >
        <div class="masterNode">
          <div class="stage-info">
            <div class="stage-icon">
              <img :src="pictureType" alt="" />
            </div>
            <div class="stage-name" :style="{ color: stageColor }">{{ item.stageName }}阶段</div>
          </div>
          <div
            class="nodes-container"
            :ref="(el) => setContainerRef(el, index)"
            @mousedown="(e) => startDrag(e, index)"
            @mousemove="onDrag"
            @mouseup="stopDrag"
            @mouseleave="stopDrag"
          >
            <div
              v-for="(node, num) in item.nodes"
              :key="num"
              class="nodes-div"
              :ref="colorJudgment(item, node, index, num)"
            >
              <div class="node" :style="{ border: borderColor, boxShadow: boxShadow, outline: outline }">
                <div class="node-content" :style="{ color: stateColor }">
                  {{ node.state }}
                </div>
                <div
                  class="node-roles"
                  :style="{ backgroundColor: backgroundColor, color: fontColor }"
                  v-if="node.role"
                >
                  {{ node.role }}
                </div>
              </div>
              <div class="arrowhead">
                <p></p>
                <p></p>
              </div>
            </div>
          </div>
        </div>
        <div class="nodeImg">
          <img :src="arrow" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.process-nodes {
  padding: 20px;
  padding-top: 0px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  .legend {
    width: 45%;
    height: 60px;
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    background: #f2f3f5;
    padding: 15px 20px;
    border-radius: 4px;

    .legend-label {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: #1d2129;
      margin-right: 20px;
    }

    .legend-items {
      display: flex;
      gap: 30px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .legend-icon {
          width: 16px;
          height: 16px;
          border-radius: 3px;

          &.new {
            border: 2px solid #52c41a;
          }

          &.update {
            border: 2px solid #faad14;
          }

          &.delete {
            border: 2px solid #ff5533;
          }
        }

        span {
          font-size: 14px;
          color: #666;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 20px;

      .collect-flow {
        color: #1890ff;
        cursor: pointer;
        font-size: 14px;
      }

      .user-info {
        color: #666;
        font-size: 14px;
      }

      .log-btn {
        background: #1890ff;
        color: white;
        border: none;
        padding: 6px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          background: #40a9ff;
        }
      }
    }
  }

  .flow-content {
    .flow-row {
      height: 100px;

      .masterNode {
        height: 80px;
        display: flex;
        background: #f7f8fa;
        border-radius: 8px;
        overflow: hidden;
      }

      .stage-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 120px;
        padding: 20px;
        background: #fafafa;

        .stage-icon {
          width: 24px;
          height: 24px;
          border-radius: 8px;
          margin-bottom: 10px;

          img {
            width: 100%;
          }
        }

        .stage-name {
          font-weight: 500;
          color: #333;
          font-size: 14px;
        }
      }

      .nodes-container {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 20px;
        flex-wrap: nowrap;
        overflow: auto;
        overflow-y: hidden;
        overflow-x: hidden;
        cursor: grab;
        -webkit-tap-highlight-color: transparent;
        /* 适用于移动端和部分桌面端 */
        user-select: none;
        /* 可选：禁止文本被选中 */

        &::-webkit-scrollbar {
          height: 5px;
          /* 水平滚动条高度 */
        }

        .nodes-div {
          height: 100%;
          display: flex;
          align-items: center;
        }

        .node {
          display: inline-block;
          min-width: 168px;
          height: 52px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #e5e6eb;
          position: relative;

          .node-content {
            display: inline-block;
            white-space: nowrap;
            height: 100%;
            font-size: 13px;
            color: #1d2129;
            line-height: 52px;
            padding: 0 10px;
          }

          .node-roles {
            background-color: #f2f3f5;
            color: #86909c;
            margin-right: 0;
            padding: 2px 3px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            line-height: 17px;
            text-align: center;
            border-radius: 2px;
            font-style: normal;
            position: absolute;
            top: 2px;
            right: 2px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .process-nodes {
    padding: 10px;

    .legend {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;

      .legend-items {
        flex-wrap: wrap;
      }
    }

    .flow-content .flow-row {
      margin-top: 20px;
      flex-direction: column;

      .stage-info {
        width: 100%;
        flex-direction: row;
        justify-content: flex-start;
        gap: 15px;
      }

      .nodes-container {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }
}

.arrow {
  width: 12px;
  height: 12px;
  margin-left: 53px;
}

.arrowhead {
  display: flex;
  align-items: center;
  height: 5px;
  margin-left: 5px;

  p {
    margin-top: 15px;
  }

  p:nth-child(1) {
    width: 16.87px;
    height: 1.67px;
    background: #c9cdd4;
  }

  p:nth-child(2) {
    border: 3.5px solid #c9cdd4;
    border-top-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
  }
}

.nodes-container .nodes-div:last-child .arrowhead {
  display: none;
}

.flow-row:last-child .nodeImg {
  display: none;
}

.nodeImg {
  width: 12px;
  margin-left: 50px;

  img {
    width: 100%;
  }
}
</style>
