<script setup lang="ts">
// 需求预览-需求对比
import { collapse, Button, message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import VisibleTable from './components/VisibleTable/index.vue';
import { miceBidManOrderList<PERSON>pi, schemeApi } from '@haierbusiness-front/apis';
import demand_stay from '@/assets/image/advisors/demand_stay.png';
import demand_place from '@/assets/image/advisors/demand_place.png';
import demand_catering from '@/assets/image/advisors/demand_catering.png';
import demand_vehicle from '@/assets/image/advisors/demand_vehicle.png';
import demand_attendant from '@/assets/image/advisors/demand_attendant.png';
import demand_activity from '@/assets/image/advisors/demand_activity.png';
import demand_insurance from '@/assets/image/advisors/demand_insurance.png';
import demand_material from '@/assets/image/advisors/demand_material.png';
import demand_traffic from '@/assets/image/advisors/demand_traffic.png';
import demand_present from '@/assets/image/advisors/demand_present.png';
import demand_other from '@/assets/image/advisors/demand_other.png';
import { CaretDownOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import type { VxeGridProps } from 'vxe-table';
import { ref, reactive, onMounted, defineProps, computed, defineExpose, defineEmits, useAttrs } from 'vue';
import {
  MiceTypeConstantO,
  SelCollapseS,
  MiceDetail,
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  MaterialTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  RoomTypeConstant,
  BreakfastTypeConstant,
  HotelDinnerTypeConstant,
  CateringTypeConstant,
  CateringTimeTypeConstant,
  HaveDrinksTypeConstant,
  CarUsageTypeConstant,
  CarBrandTypeConstant,
  SeatTypeConstant,
  UsageTimeTypeConstant,
  AttendantTypeConstant,
  MiceTypeConstant,
  ProcessNode,
} from '@haierbusiness-front/common-libs';
import { getDataBy, resolveParam } from '@haierbusiness-front/utils';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const props = defineProps({
  showTip: {
    type: Boolean,
    default: false,
  },
  miceDetail: {
    type: Object,
    default() {
      return {
        miceName: '',
        mainCode: '',
        personTotal: '',
        miceType: '',
        startDate: '',
        endDate: '',
      };
    },
  },
});
const showDetail = ref(false);
</script>
<template>
  <div class="demand_contrast_header demand_contrast_flex">
    <div class="demand_contrast_left">
      <div class="header-left">
        <div>
          <div class="interact_mice_title">
            <div class="interact_mice_name_img mr8"></div>
            <div class="interact_mice_name">
              {{ props.miceDetail.miceName || '' }}
            </div>
            <div class="scheme-node" :style="'background:' + ProcessNode.ofType(props.miceDetail.processNode).color">
              {{ ProcessNode.ofType(props.miceDetail.processNode).desc }}
            </div>
            <div class="tooltip-top" v-if="props.miceDetail.processNode === 'BID_PUSH'">
              <a-tooltip :overlayInnerStyle="{ width: '650px' }">
                <template #title
                  >竞价推送逻辑说明：<br />
                  1.竞价推送策略分为三种：①全员推送（非直签酒店可选）②级别推送（非直签酒店可选）③仅推送自己（直签酒店可选）<br />
                  2. 系统默认的推送策略为：国内：②级别推送；国外：①全员推送<br />
                  3. 推送策略说明：<br />
                  1）全员推送：当会议需求预算大于20万元时，推送所有正式服务商；当会议需求预算小于等于20万元时，推送所有服务商（包含试用期）<br />
                  2）级别推送：<br />
                  <div style="margin-left: 93px">· 每出现一个标的方案会执行一次竞价推送；</div>
                  <div style="margin-left: 93px">· 推送服务商的最小数量和最大数量均为系统配置的推送服务商数量；</div>
                  <div style="margin-left: 93px">· 服务商推送的排序规则按照标的方案金额来决定，排序逻辑如下：</div>
                  <div style="font-weight: 700; margin-left: 100px">标的方案的服务商必推送</div>
                  <div class="tooltip-compare">
                    <span>金额条件（标的的方案金额）：</span> <span>服务商推送逻辑： </span>
                  </div>
                  <div class="tooltip-compare"><span>n≥100万元</span> <span>m≥90分以上（除试用期） </span></div>
                  <div class="tooltip-compare"><span>100万元＞n≥40万元</span> <span>m≥80分以上（除试用期） </span></div>
                  <div class="tooltip-compare"><span>40万元＞n≥20万元</span> <span>m≥50分以上（除试用期） </span></div>
                  <div class="tooltip-compare"><span>n≤20万元</span> <span>m＞ 0分以上（含试用期）</span></div>

                  <div style="font-weight: 700; margin-left: 100px">
                    以上推送规则，如果对应等级服务商数量不足，则向下补位
                  </div>

                  3）仅推送自己：当直签酒店服务商关联的酒店满足用户需求时直接推送该酒店所对应的商户</template
                >
                <QuestionCircleOutlined style="font-size: 20px; margin-left: 20px; vertical-align: 90%" />
              </a-tooltip>
            </div>
          </div>
          <div class="interact_mice_num mt12">
            <span class="mr10">会议编号：{{ props.miceDetail.mainCode }}</span>
            <img @click="getCopy(props.miceDetail.mainCode)" src="@/assets/image/scheme/copy_blue.png" width="16" />
          </div>
        </div>
        <div class="demand_contrast_right" style="display: flex; align-items: center">
          <slot name="header"></slot>
        </div>
      </div>
      <div class="header-details">
        <div class="details-item">
          <span class="label mice_info_person_img"> 会议人数： </span>
          {{ props.miceDetail.personTotal }}人
        </div>

        <div class="details-item">
          <span class="label mice_info_type_img"> 会议类型：</span>
          {{ MiceTypeConstant?.ofType(props.miceDetail.miceType)?.desc }}
        </div>
        <!-- <br /> -->
        <div class="details-item">
          <span class="label mice_info_time_img"> 需求开始时间： </span>
          {{ props.miceDetail.endDate ? props.miceDetail.endDate.slice(0, 10) : '' }}至
          {{ props.miceDetail.startDate ? props.miceDetail.startDate.slice(0, 10) : '' }}
        </div>
        <div
          class="details-item"
          v-if="!['DEMAND_SUBMIT'].includes(props.miceDetail.processNode) && props.miceDetail.demandTotalPrice"
        >
          <span class="label mice_info_price_img"> 会议预算：</span>
          ¥ {{ props.miceDetail.demandTotalPrice }}
        </div>
        <div class="details-item" v-if="props.miceDetail.operatorCode">
          <span class="label mice_info_person_img"> 会议对接人： </span>
          {{ props.miceDetail.operatorName }}（{{ props.miceDetail.operatorCode }}）
        </div>
        <!-- <div class="details-item">
          <span class="label mice_info_time_img"> 需求结束时间： </span>
          {{ props.miceDetail.startDate ? props.miceDetail.startDate.slice(0, 10) : '' }}
        </div> -->

        <div
          class="show-icon"
          @click="showDetail = !showDetail"
          v-if="
            ![
              'DEMAND_SUBMIT',
              'DEMAND_RECEIVE',
              'DEMAND_PRE_INTERACT',
              'DEMAND_CONFIRM',
              'DEMAND_APPROVAL',
              'DEMAND_PUSH',
            ].includes(props.miceDetail.processNode)
          "
        >
          <CaretDownOutlined :rotate="showDetail ? 180 : 0" style="font-size: 18px" />
        </div>
      </div>
      <div v-if="props.miceDetail.remarks" class="header-details">
        <div class="details-item">
          <span class="label mice_info_time_img"> 备注： </span>
          {{ props.miceDetail.remarks }}
        </div>
      </div>
      <div class="header-details" v-if="showDetail">
        <div class="details-item" v-if="props.miceDetail.submitDeadline">
          <span class="label mice_info_time_img"> 方案互动计划截止时间： </span>
          {{ props.miceDetail.submitDeadline ? props.miceDetail.submitDeadline : '' }}
        </div>
        <a-button size="small" class="link-btn" type="link">查看变更附件</a-button>
        <div class="details-item" v-if="props.miceDetail.submitActualEndDate">
          <span class="label mice_info_time_img"> 方案互动实际截止时间： </span>
          {{ props.miceDetail.submitActualEndDate ? props.miceDetail.submitActualEndDate : '' }}
        </div>
      </div>
      <div class="header-details" v-if="showDetail">
        <div class="details-item" v-if="props.miceDetail.biddingDeadline">
          <span class="label mice_info_time_img"> 竞价计划截止时间： </span>
          {{ props.miceDetail.biddingDeadline ? props.miceDetail.biddingDeadline : '' }}
        </div>
        <a-button size="small" class="link-btn" type="link">查看变更附件</a-button>
        <div class="details-item" v-if="props.miceDetail.biddingActualEndDate">
          <span class="label mice_info_time_img"> 竞价实际截止时间： </span>
          {{ props.miceDetail.biddingActualEndDate ? props.miceDetail.biddingActualEndDate : '' }}
        </div>
      </div>

      <div class="demand_contrast_title3" v-show="showTip">
        标注：
        <div class="demand_contrast_icon1"></div>
        新增需求
        <div class="demand_contrast_icon2"></div>
        变更需求
        <div class="demand_contrast_icon3"></div>
        删除需求
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.demand_contrast_flex {
  display: flex;
  justify-content: space-between;
}
.demand_contrast_header {
  min-height: 50px;
  width: 100%;
  padding: 24px 32px;

  background: url('@/assets/image/scheme/mice_bgc.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.demand_contrast_left {
  width: 100%;
  .header-left {
    display: flex;
    justify-content: space-between;
  }
  .interact_mice_title {
    display: flex;
    align-items: center;

    .interact_mice_name_img {
      width: 28px;
      height: 28px;
      background-image: url('@/assets/image/scheme/mice_name.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .interact_mice_name {
      font-family: PingFangSCSemibold, PingFangSCSemibold;
      font-weight: normal;
      font-size: 20px;
      color: #1d2129;
      line-height: 28px;
    }

    .interact_mice_type {
      width: 108px;
      height: 28px;
      line-height: 28px;
      text-align: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      background: #1868db;
      border-radius: 4px;
    }
  }

  .interact_mice_num {
    display: flex;
    align-items: center;

    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    img {
      cursor: pointer;
    }
  }
  .interact_mice_info {
    position: relative;
    width: 100%;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    .mice_info_title {
      display: inline-block;
      text-indent: 26px;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center left;
    }
    .mice_info_person_img {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    .mice_info_type_img {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    .mice_info_time_img {
      background-image: url('@/assets/image/scheme/mice_time.png');
    }

    .mice_info_value {
      color: #1d2129;
    }
  }
  .w80 {
    width: 120%;
  }
}

.demand_contrast_title3 {
  min-width: 400px;
  white-space: nowrap;
  padding-left: 20px;
  margin-top: 20px;
  width: 40%;
  height: 40px;
  background: #f2f3f5;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  line-height: 40px;
  text-align: left;
  font-style: normal;
  display: flex;
  align-items: center;
}
.demand_contrast_icon1 {
  margin: 0 10px 0 20px;
  width: 18px;
  height: 18px;
  border: 1px solid #52c41a;
  background: #f1faef;
  display: inline-block;
}
.demand_contrast_icon2 {
  margin: 0 10px 0 20px;
  width: 18px;
  height: 18px;
  border: 1px solid #fcc04c;
  background: #fffaf1;
  display: inline-block;
}
.demand_contrast_icon3 {
  margin: 0 10px 0 20px;
  width: 18px;
  height: 18px;
  border: 1px solid #fe7b62;
  background: #fff1f0;
  display: inline-block;
}
:deep(.demand_contrast_card_item) {
  margin-top: 10px;
  width: 100%;
  background: #ffffff;
  .ant-collapse-header-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 25px;
    text-align: left;
    font-style: normal;
  }
}
.demand_contrast_footer {
  z-index: 10;
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 10px 20px;
  margin-top: 10px;
  width: calc(100% - 250px);
  text-align: right;
  background: #ffffff;
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
}
.wid100 {
  width: 100% !important;
}
.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}
.demand_contrast_footer_center {
  display: inline-block;
  text-align: center;
}
:deep(.ant-collapse-header) {
  position: relative;
  font-weight: 500;
  font-size: 20px;
  color: #1d2129;
  line-height: 28px;
}
.demand_contrast_collapse_arrow {
  position: absolute;
  top: 15px;
  left: 0px;
  width: 4px;
  height: 20px;
  background: #1868db;
}
:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

:deep(.ant-btn-primary) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  min-width: 80px;
  /* background: #1868db; */
  border-radius: 2px;
}

.demand_reject_reason {
  padding: 24px;
  margin: 0 15px 16px;
  border-radius: 12px;
}
.demand_contrast_right {
  white-space: nowrap;
}
.show-icon {
  display: inline-block;
  position: absolute;
  top: 30px;
  right: 0px;
  cursor: pointer;
}
.header-details {
  position: relative;
  // margin-top: 20px;
  .details-item {
    margin-top: 20px;
    // white-space: nowrap;
    display: inline-block;
    padding: 0 20px;
  }
  .label {
    display: inline-block;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;
    text-align: left;
    font-style: normal;

    text-indent: 26px;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center left;
    background-repeat: no-repeat;
  }
  .mice_info_person_img {
    background-image: url('@/assets/image/scheme/mice_person.png');
  }

  .mice_info_type_img {
    background-image: url('@/assets/image/scheme/mice_type.png');
  }

  .mice_info_price_img {
    background-image: url('@/assets/image/scheme/mice_price.png');
  }

  .mice_info_time_img {
    background-image: url('@/assets/image/scheme/mice_time.png');
  }
}

.scheme-node {
  vertical-align: middle;
  margin-left: 24px;
  display: inline-block;
  width: 108px;
  height: 28px;
  // background: #faad14;
  border-radius: 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  line-height: 28px;
  text-align: center;
  font-style: normal;
}
.scheme-code {
  margin-top: 12px;
  display: inline-block;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  img {
    cursor: pointer;
  }
}
.tooltip-top {
  vertical-align: -34%;
  display: inline-block;
}
.tooltip-compare {
  margin: 0 100px;
  display: flex;
  justify-content: space-between;
}
:deep(.link-btn) {
  padding: 0;
  height: 22px;
}
</style>
