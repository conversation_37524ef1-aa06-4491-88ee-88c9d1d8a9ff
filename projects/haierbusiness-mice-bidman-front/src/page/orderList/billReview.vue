<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import schemeInteract from '@haierbusiness-front/components/scheme/schemeInteract.vue';
import { meetingProcessOrchestration, resolveParam } from '@haierbusiness-front/utils';
import { Button, Dropdown, Menu, Pagination, BadgeRibbon, Tooltip, Popover, message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import meetingDetail from '@haierbusiness-front/components/mice/orderList/meetingDetail.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue';
const route = useRoute();
const frameModel = ref(inject<any>('frameModel'));
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
});
const viewSelect = ref('demand');
const presentConfirm = async () => {
  Modal.confirm({
    title: '账单复核',
    content: '是否确认全部账单？',
    async onOk() {
      const res = await miceBidManOrderListApi.userApprove({
        miceId: routeQuery.record.miceId,
        confirmState: 1,
      });
      console.log(res);
      if (res.success) {
        message.success('确认成功！');
        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
        // 跳转需求确认页面
        const url =
            (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
            '/card-order/miceOrder';
        window.location.href = url;
      }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
const open = ref(false);
const reason = ref<string>(''); // 驳回原因

const rejectList = ref();
const rejectColumns = [
  { title: '驳回', dataIndex: 'checked', key: 'checked', width: 100 },
  { title: '服务商', dataIndex: 'provider', key: 'provider', width: 200 },
  { title: '驳回原因', dataIndex: 'reason', key: 'reason' },
];
const getRejectList = async () => {
  rejectList.value = billUploadSchemeRef.value.tempDataList;
  open.value = true;
};
const cancelConfirm = async () => {
  const selected = rejectList.value.filter((item) => item.checked);
  if (selected.length === 0) {
    message.error('请至少选择一个需要驳回的服务商！');
    return;
  }
  if (selected.some((item) => !item.reason)) {
    message.error('请填写所有已选服务商的驳回原因！');
    return;
  }
  const rejectBills = selected.map((item) => ({
    rejectBillId: item.id,
    reason: item.reason,
  }));
  const res = await miceBidManOrderListApi.userConfirm({
    miceId: routeQuery.record.miceId,
    confirmState: 2,
    rejectBills: rejectBills,
  });
  console.log(res);
  if (res.success) {
    message.success('确认成功！');
    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
    // 跳转需求确认页面
    const url =
        (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
        '/card-order/miceOrder';
    window.location.href = url;
  }
  // TODO: 调用接口，传递 selected
  message.success('驳回成功！');

  open.value = false;
};
const billUploadSchemeRef = ref();
onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0;
  console.log('frameModel.value', frameModel.value);
});
</script>

<template>
  <div class="container" :style="viewSelect === 'billUpload' ? 'padding-bottom:40px' : ''">
    <!-- 方案互动 -->
    <meetingDetail
        v-if="viewSelect === 'demand'"
        type="platform"
        :class="routeQuery.record.hideBtn == '1' ? 'footer-user-width' : ''"
    />
    <billUploadScheme
        v-else-if="viewSelect === 'billUpload'"
        :platformType="'platform'"
        :class="routeQuery.record.hideBtn == '1' ? 'footer-user-width' : ''"
        ref="billUploadSchemeRef"
    ></billUploadScheme>
    <div class="footer">
      <a-radio-group v-model:value="viewSelect">
        <a-radio-button value="demand">需求视图</a-radio-button>
        <a-radio-button value="billUpload">账单视图</a-radio-button>
      </a-radio-group>
      <div v-if="viewSelect === 'demand'">
        <a-button type="primary" @click="viewSelect = 'billUpload'">下一步</a-button>
      </div>
      <div v-else>
        <a-button style="margin-right: 20px" type="primary" danger @click="getRejectList()">驳回</a-button>
        <a-button type="primary" @click="presentConfirm">确认</a-button>
      </div>
      <a-modal v-model:open="open" title="驳回原因" @ok="cancelConfirm">
        <a-table :dataSource="rejectList" :columns="rejectColumns" rowKey="id" pagination="false">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'checked'">
              <a-checkbox v-model:checked="record.checked" />
            </template>
            <template v-else-if="column.dataIndex === 'provider'">
              {{ record.merchantName }}
            </template>
            <template v-else-if="column.dataIndex === 'reason'">
              <a-input :bordered="false" v-model:value="record.reason" placeholder="请输入驳回原因" />
            </template>
          </template>
        </a-table>
      </a-modal>
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  background: #f1f2f6;
  padding: 0 auto;
}
.footer-user-width {
  margin: auto;
  width: 1280px !important;
  left: calc(50% - 640px);
}
.footer {
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  right: 0;
  background: #fff;
  left: calc(50% - 640px);
  z-index: 11;
  width: 1280px !important;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
:deep(.ant-table-thead) {
  th {
    color: #86909c !important;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }
}
:deep(.ant-table-cell) {
  padding: 12px 8px !important;
  line-height: 20px;
}
</style>
